import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Star, Award, Users } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { useCollection } from '../../hooks/useFirestore';
import { useAuth } from '../../contexts/AuthContext';
import { COLLECTIONS } from '../../services/firebase';

const AdminTestimonials = () => {
  const { user } = useAuth();
  const {
    documents: testimonialList,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument
  } = useCollection(COLLECTIONS.TESTIMONIALS, {
    orderBy: { field: 'createdAt', direction: 'desc' }
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTestimonial, setEditingTestimonial] = useState(null);
  const [selectedService, setSelectedService] = useState('all');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    client_name: '',
    client_company: '',
    client_role: '',
    client_photo_url: '',
    testimonial_text: '',
    rating: 5,
    service_used: '',
    video_url: '',
    results_achieved: '',
    featured: false
  });

  const serviceCategories = [
    { value: 'Software Development', label: 'Software Development' },
    { value: 'Video Production & Photography', label: 'Video Production & Photography' },
    { value: 'Media Production', label: 'Media Production' },
    { value: 'Photography & Design', label: 'Photography & Design' },
    { value: 'Music Studio Recording', label: 'Music Studio Recording' },
    { value: 'Graphic Design & Printing', label: 'Graphic Design & Printing' },
    { value: 'Digital Marketing', label: 'Digital Marketing' },
    { value: 'Web Development', label: 'Web Development' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseInt(value) : value)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const testimonialData = {
        client_name: formData.client_name,
        client_company: formData.client_company,
        client_role: formData.client_role,
        client_photo_url: formData.client_photo_url || null,
        testimonial_text: formData.testimonial_text,
        rating: parseInt(formData.rating) || 5,
        service_used: formData.service_used,
        video_url: formData.video_url || null,
        results_achieved: formData.results_achieved,
        featured: formData.featured
      };

      if (editingTestimonial) {
        // Update existing testimonial
        const result = await updateDocument(editingTestimonial.id, testimonialData);
        if (!result.success) {
          throw new Error(result.error);
        }
      } else {
        // Add new testimonial
        const result = await addDocument(testimonialData, user?.uid);
        if (!result.success) {
          throw new Error(result.error);
        }
      }

      handleCloseModal();
    } catch (error) {
      console.error('Error saving testimonial:', error);
      alert('Error saving testimonial: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (testimonial) => {
    setEditingTestimonial(testimonial);
    setFormData({
      client_name: testimonial.client_name,
      client_company: testimonial.client_company,
      client_role: testimonial.client_role,
      client_photo_url: testimonial.client_photo_url || '',
      testimonial_text: testimonial.testimonial_text,
      rating: testimonial.rating,
      service_used: testimonial.service_used,
      video_url: testimonial.video_url || '',
      results_achieved: testimonial.results_achieved,
      featured: testimonial.featured
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (testimonial) => {
    if (window.confirm(`Are you sure you want to delete the testimonial from "${testimonial.client_name}"?`)) {
      try {
        const result = await deleteDocument(testimonial.id);
        if (!result.success) {
          throw new Error(result.error);
        }
      } catch (error) {
        console.error('Error deleting testimonial:', error);
        alert('Error deleting testimonial: ' + error.message);
      }
    }
  };

  const handleView = (testimonial) => {
    // For now, just open edit modal in view mode
    handleEdit(testimonial);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingTestimonial(null);
    setFormData({
      client_name: '',
      client_company: '',
      client_role: '',
      client_photo_url: '',
      testimonial_text: '',
      rating: 5,
      service_used: '',
      video_url: '',
      results_achieved: '',
      featured: false
    });
  };

  const handleAddNew = () => {
    setEditingTestimonial(null);
    setFormData({
      client_name: '',
      client_company: '',
      client_role: '',
      client_photo_url: '',
      testimonial_text: '',
      rating: 5,
      service_used: '',
      video_url: '',
      results_achieved: '',
      featured: false
    });
    setIsModalOpen(true);
  };

  // Filter testimonials by service
  const filteredTestimonials = selectedService === 'all' 
    ? testimonialList 
    : testimonialList.filter(testimonial => testimonial.service_used === selectedService);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const columns = [
    {
      key: 'client_name',
      label: 'CLIENT',
      sortable: true,
      render: (name, testimonial) => (
        <div>
          <div className="font-bold">{name}</div>
          <div className="text-sm text-gray-600">{testimonial.client_role}</div>
          <div className="text-sm text-gray-600">{testimonial.client_company}</div>
        </div>
      )
    },
    {
      key: 'service_used',
      label: 'SERVICE',
      sortable: true,
      render: (service) => (
        <span className="px-2 py-1 text-xs font-bold bg-blue-100 text-blue-800 brutal-border">
          {service}
        </span>
      )
    },
    {
      key: 'rating',
      label: 'RATING',
      sortable: true,
      render: (rating) => (
        <div className="flex items-center gap-1">
          {renderStars(rating)}
          <span className="ml-2 font-bold">{rating}/5</span>
        </div>
      )
    },
    {
      key: 'testimonial_text',
      label: 'TESTIMONIAL',
      render: (text) => (
        <span className="text-sm">
          {text.length > 100 ? `${text.substring(0, 100)}...` : text}
        </span>
      )
    },
    {
      key: 'featured',
      label: 'STATUS',
      render: (featured) => (
        <span className={`px-2 py-1 text-xs font-bold brutal-border ${
          featured ? 'bg-yellow-400 text-black' : 'bg-gray-100 text-gray-800'
        }`}>
          {featured ? 'FEATURED' : 'STANDARD'}
        </span>
      )
    },
    {
      key: 'created_date',
      label: 'CREATED',
      sortable: true,
      render: (date) => (
        <span className="text-sm">
          {new Date(date).toLocaleDateString('en-GB')}
        </span>
      )
    }
  ];

  const getServiceCounts = () => {
    const counts = { all: testimonialList.length };
    serviceCategories.forEach(service => {
      counts[service.value] = testimonialList.filter(t => t.service_used === service.value).length;
    });
    return counts;
  };

  const serviceCounts = getServiceCounts();
  const averageRating = testimonialList.reduce((sum, t) => sum + t.rating, 0) / testimonialList.length;
  const featuredCount = testimonialList.filter(t => t.featured).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">TESTIMONIALS MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Manage client testimonials and reviews
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW TESTIMONIAL
          </ActionButton>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-blue-500 text-white p-3 brutal-border brutal-shadow-small">
              <Users className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{testimonialList.length}</div>
              <div className="font-bold text-gray-600 text-sm">TOTAL TESTIMONIALS</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-yellow-500 text-black p-3 brutal-border brutal-shadow-small">
              <Star className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{averageRating.toFixed(1)}</div>
              <div className="font-bold text-gray-600 text-sm">AVERAGE RATING</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-green-500 text-white p-3 brutal-border brutal-shadow-small">
              <Award className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{featuredCount}</div>
              <div className="font-bold text-gray-600 text-sm">FEATURED</div>
            </div>
          </div>
        </div>
      </div>

      {/* Service Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedService('all')}
            className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
              selectedService === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-black hover:bg-gray-50'
            }`}
          >
            ALL ({serviceCounts.all})
          </button>
          
          {serviceCategories.map((service) => (
            <button
              key={service.value}
              onClick={() => setSelectedService(service.value)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedService === service.value
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {service.label} ({serviceCounts[service.value] || 0})
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredTestimonials}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={false}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingTestimonial ? 'EDIT TESTIMONIAL' : 'ADD NEW TESTIMONIAL'}
        size="large"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Client Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Client Name"
              name="client_name"
              value={formData.client_name}
              onChange={handleInputChange}
              placeholder="Enter client name"
              required
            />
            
            <FormField
              label="Client Company"
              name="client_company"
              value={formData.client_company}
              onChange={handleInputChange}
              placeholder="Enter company name"
              required
            />
            
            <FormField
              label="Client Role"
              name="client_role"
              value={formData.client_role}
              onChange={handleInputChange}
              placeholder="e.g., CEO, Marketing Manager"
              required
            />
            
            <FormField
              label="Client Photo URL"
              name="client_photo_url"
              value={formData.client_photo_url}
              onChange={handleInputChange}
              placeholder="Enter photo URL"
            />
          </div>
          
          {/* Testimonial Content */}
          <FormField
            label="Testimonial Text"
            type="textarea"
            name="testimonial_text"
            value={formData.testimonial_text}
            onChange={handleInputChange}
            placeholder="Enter the client's testimonial"
            rows={4}
            required
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Service Used"
              type="select"
              name="service_used"
              value={formData.service_used}
              onChange={handleInputChange}
              options={serviceCategories}
              required
            />
            
            <FormField
              label="Rating"
              type="select"
              name="rating"
              value={formData.rating}
              onChange={handleInputChange}
              options={[
                { value: 1, label: '1 Star' },
                { value: 2, label: '2 Stars' },
                { value: 3, label: '3 Stars' },
                { value: 4, label: '4 Stars' },
                { value: 5, label: '5 Stars' }
              ]}
              required
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Results Achieved"
              name="results_achieved"
              value={formData.results_achieved}
              onChange={handleInputChange}
              placeholder="Brief description of results"
              required
            />
            
            <FormField
              label="Video URL (optional)"
              name="video_url"
              value={formData.video_url}
              onChange={handleInputChange}
              placeholder="Enter video testimonial URL"
            />
          </div>
          
          <FormField
            label="Featured Testimonial"
            type="checkbox"
            name="featured"
            value={formData.featured}
            onChange={handleInputChange}
          />
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingTestimonial ? Edit : Plus}
            >
              {editingTestimonial ? 'UPDATE TESTIMONIAL' : 'ADD TESTIMONIAL'}
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminTestimonials;
