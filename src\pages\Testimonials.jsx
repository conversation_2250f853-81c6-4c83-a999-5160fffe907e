import React, { useState } from "react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Star, Quote, ArrowUpRight, Filter } from "lucide-react";
import { useRealtimeCollection } from "../hooks/useFirestore";
import { COLLECTIONS } from "../services/firebase";

export default function Testimonials() {
  const { documents: testimonials, loading, error } = useRealtimeCollection(COLLECTIONS.TESTIMONIALS, {
    orderBy: { field: 'createdAt', direction: 'desc' }
  });

  const [selectedService, setSelectedService] = useState("all");

  const services = [
    { value: "all", label: "ALL SERVICES" },
    { value: "Paid Advertising", label: "PAID ADVERTISING" },
    { value: "Content Creation", label: "CONTENT CREATION" },
    { value: "Community Management", label: "COMMUNITY MANAGEMENT" },
    { value: "Analytics & Reporting", label: "ANALYTICS & REPORTING" }
  ];

  const filteredTestimonials = selectedService === "all"
    ? (testimonials || [])
    : (testimonials || []).filter(testimonial => testimonial.service_used === selectedService);

  const featuredTestimonials = (testimonials || []).filter(testimonial => testimonial.featured);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-5 h-5 ${index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getServiceColor = (service) => {
    const colors = {
      "Paid Advertising": "bg-blue-500",
      "Content Creation": "bg-pink-500",
      "Community Management": "bg-green-400",
      "Analytics & Reporting": "bg-yellow-400"
    };
    return colors[service] || "bg-gray-500";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-white brutal-border brutal-shadow p-8">
          <div className="brutal-text text-2xl text-gray-400">LOADING TESTIMONIALS...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-white brutal-border brutal-shadow p-8">
          <div className="brutal-text text-2xl mb-4">ERROR LOADING TESTIMONIALS</div>
          <div className="text-red-500 font-bold">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-green-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="asymmetric-grid">
            <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block">
              <h1 className="brutal-text text-4xl md:text-6xl mb-4">CLIENT SUCCESS STORIES</h1>
              <p className="text-xl font-bold max-w-2xl mx-auto">
                Real results from real businesses. See what our clients say about working with On-GeneralServices.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-4 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-2xl md:text-3xl">FEATURED SUCCESS STORIES</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredTestimonials.map((testimonial, index) => (
              <div 
                key={testimonial.id} 
                className={`${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
              >
                <div className="bg-white brutal-border brutal-shadow p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                  {/* Quote Icon */}
                  <div className="bg-yellow-400 w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center mb-6">
                    <Quote className="w-8 h-8 text-black" />
                  </div>

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {renderStars(testimonial.rating || 5)}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-lg font-bold text-gray-800 mb-6 leading-relaxed">
                    "{testimonial.testimonial_text || 'No testimonial text available.'}"
                  </blockquote>

                  {/* Results */}
                  <div className="bg-gray-100 p-4 brutal-border brutal-shadow-small mb-6">
                    <h4 className="brutal-text text-sm mb-2">RESULTS ACHIEVED:</h4>
                    <p className="font-bold text-green-600">{testimonial.results_achieved || 'No results specified.'}</p>
                  </div>

                  {/* Client Info */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="brutal-text text-lg">{testimonial.client_name || 'Anonymous'}</div>
                      <div className="font-bold text-gray-600">{testimonial.client_role || ''}</div>
                      <div className="font-bold text-gray-600">{testimonial.client_company || ''}</div>
                    </div>
                    
                    <div className={`${getServiceColor(testimonial.service_used)} text-white px-3 py-1 brutal-text text-xs`}>
                      {(testimonial.service_used || 'GENERAL').toUpperCase()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Filter */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {services.map((service) => (
              <button
                key={service.value}
                onClick={() => setSelectedService(service.value)}
                className={`px-6 py-3 brutal-text text-sm brutal-border brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  selectedService === service.value
                    ? 'bg-pink-500 text-white'
                    : 'bg-white text-black hover:bg-yellow-400'
                }`}
              >
                {service.label}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* All Testimonials Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredTestimonials.map((testimonial, index) => (
              <div 
                key={testimonial.id} 
                className={`${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
              >
                <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full flex flex-col">
                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {renderStars(testimonial.rating || 5)}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="font-bold text-gray-800 mb-4 flex-1 leading-relaxed">
                    "{testimonial.testimonial_text || 'No testimonial text available.'}"
                  </blockquote>

                  {/* Results Badge */}
                  <div className="bg-green-100 text-green-800 p-3 brutal-border brutal-shadow-small mb-4">
                    <div className="brutal-text text-xs mb-1">RESULTS:</div>
                    <div className="font-bold text-sm">{testimonial.results_achieved || 'No results specified.'}</div>
                  </div>

                  {/* Client Info */}
                  <div className="border-t-2 border-black pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="brutal-text text-sm">{testimonial.client_name || 'Anonymous'}</div>
                        <div className="font-bold text-xs text-gray-600">{testimonial.client_role || ''}</div>
                        <div className="font-bold text-xs text-gray-600">{testimonial.client_company || ''}</div>
                      </div>
                      
                      <div className={`${getServiceColor(testimonial.service_used)} text-white px-2 py-1 brutal-text text-xs`}>
                        {(testimonial.service_used || 'GENERAL').split(' ')[0]}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO BE OUR NEXT SUCCESS STORY?
            </h2>
            <p className="text-xl font-bold mb-8">
              Join hundreds of businesses that have transformed their social media into a revenue-generating machine.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to={createPageUrl("Contact")}
                className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                GET YOUR FREE STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Link>
              
              <Link 
                to={createPageUrl("CaseStudies")}
                className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                VIEW DETAILED CASE STUDIES
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
