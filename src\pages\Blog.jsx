import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { createPageUrl, formatDate } from "@/utils";
import { Clock, ArrowUpRight, Search, Filter } from "lucide-react";
import { useRealtimeCollection } from "../hooks/useFirestore";
import { COLLECTIONS } from "../services/firebase";

export default function Blog() {
  const { documents: allBlogPosts, loading, error } = useRealtimeCollection(COLLECTIONS.BLOG_POSTS, {
    orderBy: { field: 'publishedAt', direction: 'desc' }
  });

  // Filter published posts in the component to avoid composite index requirement
  const blogPosts = allBlogPosts ? allBlogPosts.filter(post => post.published === true) : [];

  const [filteredPosts, setFilteredPosts] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { value: "all", label: "ALL POSTS" },
    { value: "strategy", label: "STRATEGY" },
    { value: "tips", label: "TIPS" },
    { value: "case-studies", label: "CASE STUDIES" },
    { value: "industry-news", label: "INDUSTRY NEWS" },
    { value: "tutorials", label: "TUTORIALS" }
  ];

  useEffect(() => {
    filterPosts();
  }, [searchTerm, selectedCategory, blogPosts]);

  const filterPosts = () => {
    if (!blogPosts) {
      setFilteredPosts([]);
      return;
    }

    let filtered = blogPosts;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (post.tags && post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    setFilteredPosts(filtered);
  };

  const getCategoryColor = (category) => {
    const colors = {
      strategy: "bg-blue-500",
      tips: "bg-green-400",
      "case-studies": "bg-pink-500",
      "industry-news": "bg-yellow-400",
      tutorials: "bg-purple-500"
    };
    return colors[category] || "bg-gray-500";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-white brutal-border brutal-shadow p-8">
          <div className="brutal-text text-2xl text-gray-400">LOADING BLOG POSTS...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="bg-white brutal-border brutal-shadow p-8">
          <div className="brutal-text text-2xl mb-4">ERROR LOADING BLOG</div>
          <div className="text-red-500 font-bold">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="asymmetric-grid">
            <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block">
              <h1 className="brutal-text text-4xl md:text-6xl mb-4">GROWTH BLOG</h1>
              <p className="text-xl font-bold max-w-2xl mx-auto">
                Real strategies, real results, real talk about social media marketing that actually works.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.value}
                  onClick={() => setSelectedCategory(category.value)}
                  className={`px-4 py-2 brutal-text text-sm brutal-border brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                    selectedCategory === category.value
                      ? 'bg-pink-500 text-white'
                      : 'bg-white text-black hover:bg-yellow-400'
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-gray-200 p-8 brutal-border brutal-shadow inline-block">
                <h3 className="brutal-text text-2xl mb-4">NO POSTS FOUND</h3>
                <p className="font-bold text-gray-600">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <article 
                  key={post.id} 
                  className={`group cursor-pointer ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
                >
                  <div className="bg-white brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                    
                    {/* Featured Image Placeholder */}
                    <div className="bg-gray-200 h-48 brutal-border-b flex items-center justify-center">
                      <div className="text-gray-500 brutal-text text-sm">FEATURED IMAGE</div>
                    </div>

                    <div className="p-6">
                      {/* Category Badge */}
                      <div className={`${getCategoryColor(post.category)} text-white px-3 py-1 brutal-text text-xs inline-block mb-4`}>
                        {post.category.toUpperCase()}
                      </div>

                      {/* Title */}
                      <h2 className="brutal-text text-xl mb-3 group-hover:text-pink-500 transition-colors duration-200">
                        {post.title}
                      </h2>

                      {/* Excerpt */}
                      <p className="font-bold text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>

                      {/* Meta Info */}
                      <div className="flex items-center justify-between text-sm font-bold text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{post.read_time} min read</span>
                        </div>
                        <span>{formatDate(post.created_date)}</span>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.slice(0, 3).map((tag, tagIndex) => (
                          <span 
                            key={tagIndex}
                            className="bg-gray-100 text-gray-700 px-2 py-1 text-xs font-bold"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>

                      {/* Read More Link */}
                      <Link 
                        to={`${createPageUrl("BlogPost")}/${post.slug || post.id}`}
                        className="bg-blue-500 text-white px-4 py-2 brutal-text text-sm inline-flex items-center gap-2 hover:bg-pink-500 transition-colors duration-200"
                      >
                        READ MORE
                        <ArrowUpRight className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              GET WEEKLY GROWTH TIPS
            </h2>
            <p className="text-xl font-bold mb-8">
              Join 10,000+ marketers getting actionable strategies delivered to their inbox every Tuesday.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
              />
              <button className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150">
                SUBSCRIBE
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
